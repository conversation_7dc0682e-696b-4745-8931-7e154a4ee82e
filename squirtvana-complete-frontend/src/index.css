@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Custom Squirtvana Styles */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-indicator {
  @apply w-3 h-3 rounded-full;
}

.status-online {
  @apply bg-green-500;
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
}

.status-offline {
  @apply bg-red-500;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .mobile-padding {
    @apply px-4 py-2;
  }
  
  .mobile-text {
    @apply text-sm;
  }
}

/* Touch optimizations */
.touch-target {
  @apply min-h-[44px] min-w-[44px];
}

/* Animations */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
  }
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}

