import json
import websocket
from flask import Blueprint, request, jsonify

obs_bp = Blueprint('obs', __name__)

OBS_WEBSOCKET_URL = "ws://localhost:4455"

def send_obs_command(command, data=None):
    """Send command to OBS via WebSocket"""
    try:
        ws = websocket.create_connection(OBS_WEBSOCKET_URL, timeout=5)
        
        message = {
            "op": 6,
            "d": {
                "requestType": command,
                "requestId": "squirtvana-request",
                "requestData": data or {}
            }
        }
        
        ws.send(json.dumps(message))
        response = ws.recv()
        ws.close()
        
        return json.loads(response)
        
    except Exception as e:
        print(f"OBS WebSocket error: {e}")
        return None

@obs_bp.route('/change-scene', methods=['POST'])
def change_scene():
    try:
        data = request.get_json()
        scene_name = data.get('scene', '')
        
        if not scene_name:
            return jsonify({'success': False, 'error': 'No scene name provided'})
        
        # Map scene values to actual scene names
        scene_mapping = {
            'cam1': 'Cam 1',
            'pussy_closeup': 'Pussy Closeup',
            'full_body': 'Full Body',
            'face_cam': 'Face Cam',
            'toys': 'Toys Scene',
            'bed': 'Bed Scene'
        }
        
        actual_scene_name = scene_mapping.get(scene_name, scene_name)
        
        response = send_obs_command("SetCurrentProgramScene", {
            "sceneName": actual_scene_name
        })
        
        if response:
            return jsonify({
                'success': True,
                'message': f'Scene changed to {actual_scene_name}'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to communicate with OBS'
            })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@obs_bp.route('/update-text', methods=['POST'])
def update_text():
    try:
        data = request.get_json()
        source_name = data.get('source', 'DirtyTalk')
        text = data.get('text', '')
        
        response = send_obs_command("SetInputSettings", {
            "inputName": source_name,
            "inputSettings": {
                "text": text
            }
        })
        
        if response:
            return jsonify({
                'success': True,
                'message': f'Text updated for {source_name}'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to update text source'
            })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@obs_bp.route('/status')
def obs_status():
    try:
        response = send_obs_command("GetVersion")
        connected = response is not None
        
        return jsonify({
            'status': 'active',
            'connected': connected,
            'message': 'OBS connected' if connected else 'OBS not connected'
        })
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'connected': False,
            'error': str(e)
        })

