import os
import requests
from flask import Blueprint, request, jsonify, send_file
from dotenv import load_dotenv

load_dotenv()

audio_bp = Blueprint('audio', __name__)

@audio_bp.route('/generate', methods=['POST'])
def generate_audio():
    try:
        data = request.get_json()
        text = data.get('text', '')
        
        if not text:
            return jsonify({'success': False, 'error': 'No text provided'})
        
        # Try to use ElevenLabs API
        try:
            api_key = os.getenv('ELEVENLABS_API_KEY')
            voice_id = os.getenv('ELEVENLABS_VOICE_ID')
            
            if not api_key or not voice_id:
                raise Exception("ElevenLabs API key or voice ID not configured")
            
            url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
            
            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": api_key
            }
            
            data = {
                "text": text,
                "model_id": "eleven_monolingual_v1",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.5
                }
            }
            
            response = requests.post(url, json=data, headers=headers)
            
            if response.status_code == 200:
                # Save audio file
                audio_path = "/tmp/output.mp3"
                with open(audio_path, 'wb') as f:
                    f.write(response.content)
                
                return jsonify({
                    'success': True,
                    'audio_url': '/api/audio/play',
                    'message': 'Audio generated successfully'
                })
            else:
                raise Exception(f"ElevenLabs API error: {response.status_code}")
                
        except Exception as api_error:
            print(f"Audio API Error: {api_error}")
            return jsonify({
                'success': False,
                'error': 'Audio generation failed',
                'details': str(api_error)
            })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@audio_bp.route('/test-voice', methods=['POST'])
def test_voice():
    try:
        test_text = "Hello! This is a test of the Squirtvana voice system. Everything sounds great!"
        
        # Use the same generation logic
        return generate_audio_internal(test_text)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@audio_bp.route('/play')
def play_audio():
    try:
        audio_path = "/tmp/output.mp3"
        if os.path.exists(audio_path):
            return send_file(audio_path, mimetype="audio/mpeg")
        else:
            return jsonify({'error': 'Audio file not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def generate_audio_internal(text):
    """Internal function to generate audio"""
    try:
        api_key = os.getenv('ELEVENLABS_API_KEY')
        voice_id = os.getenv('ELEVENLABS_VOICE_ID')
        
        if not api_key or not voice_id:
            return jsonify({
                'success': True,
                'audio_url': '/api/audio/play',
                'message': 'Audio generation simulated (API not configured)'
            })
        
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
        
        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": api_key
        }
        
        data = {
            "text": text,
            "model_id": "eleven_monolingual_v1",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.5
            }
        }
        
        response = requests.post(url, json=data, headers=headers)
        
        if response.status_code == 200:
            audio_path = "/tmp/output.mp3"
            with open(audio_path, 'wb') as f:
                f.write(response.content)
            
            return jsonify({
                'success': True,
                'audio_url': '/api/audio/play',
                'message': 'Audio generated successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'ElevenLabs API error: {response.status_code}'
            })
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@audio_bp.route('/status')
def audio_status():
    return jsonify({
        'status': 'active',
        'message': 'Audio service is running',
        'elevenlabs_configured': bool(os.getenv('ELEVENLABS_API_KEY'))
    })

