import os
import psutil
import requests
from flask import Blueprint, jsonify
from dotenv import load_dotenv

load_dotenv()

system_bp = Blueprint('system', __name__)

@system_bp.route('/system/stats')
def get_system_stats():
    try:
        # Get system statistics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return jsonify({
            'success': True,
            'cpu': round(cpu_percent, 1),
            'memory': round(memory.percent, 1),
            'disk': round(disk.percent, 1),
            'memory_total': memory.total,
            'memory_used': memory.used,
            'disk_total': disk.total,
            'disk_used': disk.used
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'cpu': 0,
            'memory': 0,
            'disk': 0
        })

@system_bp.route('/system/telegram-status')
def telegram_status():
    try:
        api_key = os.getenv('TELEGRAM_API_KEY')
        
        if not api_key:
            return jsonify({
                'active': False,
                'message': 'Telegram API key not configured'
            })
        
        # Test Telegram bot API
        url = f"https://api.telegram.org/bot{api_key}/getMe"
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                return jsonify({
                    'active': True,
                    'message': 'Telegram bot is active',
                    'bot_info': data.get('result', {})
                })
        
        return jsonify({
            'active': False,
            'message': 'Telegram bot not responding'
        })
        
    except Exception as e:
        return jsonify({
            'active': False,
            'message': f'Telegram check failed: {str(e)}'
        })

@system_bp.route('/system/info')
def system_info():
    try:
        import platform
        import sys
        
        return jsonify({
            'success': True,
            'system': platform.system(),
            'platform': platform.platform(),
            'python_version': sys.version,
            'cpu_count': psutil.cpu_count(),
            'memory_total': psutil.virtual_memory().total,
            'disk_total': psutil.disk_usage('/').total
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@system_bp.route('/system/processes')
def system_processes():
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                proc_info = proc.info
                if proc_info['cpu_percent'] > 0 or proc_info['memory_percent'] > 1:
                    processes.append(proc_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        # Sort by CPU usage
        processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
        
        return jsonify({
            'success': True,
            'processes': processes[:10]  # Top 10 processes
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'processes': []
        })

