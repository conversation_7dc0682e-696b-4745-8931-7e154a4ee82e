import os
from flask import Blueprint, request, jsonify
from dotenv import load_dotenv

load_dotenv()

gpt_bp = Blueprint('gpt', __name__)

@gpt_bp.route('/generate', methods=['POST'])
def generate_dirty_talk():
    try:
        data = request.get_json()
        prompt = data.get('prompt', '')
        
        if not prompt:
            return jsonify({'success': False, 'error': 'No prompt provided'})
        
        # Try to use OpenAI API
        try:
            import openai
            
            # Configure OpenAI client for OpenRouter
            client = openai.OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=os.getenv('OPENROUTER_KEY')
            )
            
            response = client.chat.completions.create(
                model="meta-llama/llama-3.1-8b-instruct:free",
                messages=[
                    {"role": "system", "content": "You are a creative and flirty AI assistant that generates engaging, playful, and slightly provocative responses. Keep responses fun and entertaining while being respectful."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.8
            )
            
            generated_text = response.choices[0].message.content
            
        except Exception as api_error:
            print(f"API Error: {api_error}")
            # Fallback response
            generated_text = f"✨ AI Response to: {prompt}\n\nHey there! I'm feeling a bit shy right now, but I'd love to chat with you more. Maybe we can explore some fun topics together? 😉"
        
        return jsonify({
            'success': True,
            'generated_text': generated_text
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@gpt_bp.route('/status')
def gpt_status():
    return jsonify({
        'status': 'active',
        'message': 'GPT service is running',
        'api_key_configured': bool(os.getenv('OPENROUTER_KEY'))
    })

