import json
import websocket
from flask import Blueprint, jsonify

stream_bp = Blueprint('stream', __name__)

OBS_WEBSOCKET_URL = "ws://localhost:4455"

def send_obs_command(command, data=None):
    """Send command to OBS via WebSocket"""
    try:
        ws = websocket.create_connection(OBS_WEBSOCKET_URL, timeout=5)
        
        message = {
            "op": 6,
            "d": {
                "requestType": command,
                "requestId": "squirtvana-stream",
                "requestData": data or {}
            }
        }
        
        ws.send(json.dumps(message))
        response = ws.recv()
        ws.close()
        
        return json.loads(response)
        
    except Exception as e:
        print(f"OBS WebSocket error: {e}")
        return None

@stream_bp.route('/stream/start', methods=['POST'])
def start_stream():
    try:
        response = send_obs_command("StartStream")
        
        if response:
            return jsonify({
                'success': True,
                'message': 'Stream started successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to start stream'
            })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@stream_bp.route('/stream/stop', methods=['POST'])
def stop_stream():
    try:
        response = send_obs_command("StopStream")
        
        if response:
            return jsonify({
                'success': True,
                'message': 'Stream stopped successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to stop stream'
            })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@stream_bp.route('/recording/start', methods=['POST'])
def start_recording():
    try:
        response = send_obs_command("StartRecord")
        
        if response:
            return jsonify({
                'success': True,
                'message': 'Recording started successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to start recording'
            })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@stream_bp.route('/recording/stop', methods=['POST'])
def stop_recording():
    try:
        response = send_obs_command("StopRecord")
        
        if response:
            return jsonify({
                'success': True,
                'message': 'Recording stopped successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to stop recording'
            })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@stream_bp.route('/stream/status')
def stream_status():
    try:
        response = send_obs_command("GetStreamStatus")
        
        if response and 'd' in response and 'responseData' in response['d']:
            data = response['d']['responseData']
            return jsonify({
                'success': True,
                'streaming': data.get('outputActive', False),
                'recording': False  # Would need separate call for recording status
            })
        else:
            return jsonify({
                'success': False,
                'streaming': False,
                'recording': False,
                'error': 'Could not get stream status'
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'streaming': False,
            'recording': False,
            'error': str(e)
        })

