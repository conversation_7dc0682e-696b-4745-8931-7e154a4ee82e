import os
import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from flask import Flask, send_from_directory, jsonify
    from flask_cors import CORS
    from dotenv import load_dotenv
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please ensure all dependencies are installed: pip install -r requirements.txt")
    sys.exit(1)

# Load environment variables
load_dotenv()

# Import route blueprints with error handling
try:
    from routes.gpt import gpt_bp
    from routes.audio import audio_bp
    from routes.obs import obs_bp
    from routes.stream import stream_bp
    from routes.system import system_bp
except ImportError as e:
    print(f"Error importing route modules: {e}")
    print("Some features may not be available.")

app = Flask(__name__, static_folder='static', static_url_path='')

# Enable CORS for all routes
CORS(app, origins="*")

# Register blueprints with error handling
try:
    app.register_blueprint(gpt_bp, url_prefix='/api/gpt')
    app.register_blueprint(audio_bp, url_prefix='/api/audio')
    app.register_blueprint(obs_bp, url_prefix='/api/obs')
    app.register_blueprint(stream_bp, url_prefix='/api')
    app.register_blueprint(system_bp, url_prefix='/api')
except NameError:
    print("Warning: Some route blueprints could not be registered")

# Serve React app
@app.route('/')
def serve_react_app():
    try:
        return send_from_directory(app.static_folder, 'index.html')
    except:
        return jsonify({'message': 'Frontend not built yet. Run: cd frontend && npm run build'})

@app.route('/<path:path>')
def serve_react_assets(path):
    if path != "" and os.path.exists(os.path.join(app.static_folder, path)):
        return send_from_directory(app.static_folder, path)
    else:
        return serve_react_app()

@app.route('/api/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'Squirtvana PWA Backend is running',
        'python_version': sys.version,
        'flask_version': getattr(__import__('flask'), '__version__', 'unknown')
    })

if __name__ == '__main__':
    print("🚀 Starting Squirtvana PWA Backend...")
    print("📱 Frontend available at: http://localhost:5000")
    print("🔌 API available at: http://localhost:5000/api")
    print(f"🐍 Python: {sys.version}")
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

